// Imports
import { Env } from 'src/config/env';
import * as CryptoJS from 'crypto-js';
import { HTTPError } from 'src/config/error';

export function decryptText(text: string) {
  const bytes = CryptoJS.AES.decrypt(text, Env.crypt.sysEncKey);
  return bytes.toString(CryptoJS.enc.Utf8);
}

export let numberCodes;
export function initializeNumberCodes() {
  numberCodes = decryptText(Env.crypt.phoneEncKey);
  numberCodes = JSON.parse(numberCodes);
}

export function decryptPhone(encPhone: string) {
  if (!encPhone) throw HTTPError({ value: 'encPhone' });
  if (typeof encPhone !== 'string') throw HTTPError({ value: 'encPhone' });

  if (!numberCodes) {
    initializeNumberCodes();
  }

  let decryptStr = '';
  const finalStr = encPhone.split('===')[0];
  const splitArr = finalStr.match(/.{3}/g);

  for (let i = 0; i < splitArr?.length; i++) {
    const element = splitArr[i];
    Object.keys(numberCodes).map(function (key) {
      try {
        if (numberCodes[key].includes(element)) decryptStr += key;
      } catch (error) {}
    });
  }
  return finalStr.length % decryptStr.length === 0 ? decryptStr : encPhone;
}
