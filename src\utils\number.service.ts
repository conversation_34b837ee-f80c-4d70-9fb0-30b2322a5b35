// Imports
import { Injectable } from '@nestjs/common';

@Injectable()
export class NumberService {
  constructor() {}

  withCommas(x, rupeeSymbol = false) {
    const withCommaStr = this.amountNumberWithCommas(x);

    if (rupeeSymbol) {
      return '₹' + withCommaStr;
    }

    return withCommaStr;
  }

  private amountNumberWithCommas(x) {
    try {
      let amount = typeof x != 'string' ? x.toString() : x;
      if (amount.includes('.')) amount = (+amount).toFixed(2);
      if (amount.length < 6)
        return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      else {
        amount = amount.toString().replace(/\B(?=(\d{2})+(?!\d))/g, ',');
        let tempAmount = '';
        let isCommas = false;
        for (let index = amount.length - 1; index >= 0; index--) {
          const element = amount[index];
          if (element.includes('L')) continue;
          if (element == ',') isCommas = true;
          else if (isCommas) {
            isCommas = false;
            tempAmount += element + ',';
          } else tempAmount += element;
        }
        let finalAmount = '';
        for (let index = tempAmount.length - 1; index >= 0; index--) {
          const element = tempAmount[index];
          finalAmount += element;
        }
        if (finalAmount.startsWith(','))
          finalAmount = finalAmount.replace(',', '');
        return finalAmount;
      }
    } catch (error) {
      return x;
    }
  }
}
