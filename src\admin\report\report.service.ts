// Imports
import { Injectable } from '@nestjs/common';
import { ReportQueryService } from './report.query';
import { TransactionEntity } from 'src/database/pg/entities/transaction.entity';
import { DateService } from 'src/utils/date.service';
import { raiseNotFound, raiseParamMissing } from 'src/config/error';
import { kSystem, SYSTEM_ADMIN_ID } from 'src/constant/global';
import { decryptPhone } from 'src/utils/crypt';
import { FileService } from 'src/utils/file.service';
import { kPercent } from 'src/constant/strings';
import { NumberService } from 'src/utils/number.service';

@Injectable()
export class ReportService {
  constructor(
    private readonly query: ReportQueryService,
    private readonly dateService: DateService,
    private readonly fileService: FileService,
    private readonly numService: NumberService,
  ) {}

  async collectionEfficiency(reqData) {
    const data = await this.query.dataForCollectionEfficiency(reqData);

    let expected_same_month_due_principal = 0;
    let expected_same_month_due_interest = 0;
    let paid_same_month_due_principal = 0;

    for (let index = 0; index < data.currentMonthEmiList.length; index++) {
      const emiData = data.currentMonthEmiList[index];
      const isDelayed = emiData.payment_due_status == '1';
      const isFullPay = emiData.pay_type == 'FULLPAY';
      let paid_principal = 0;
      let paid_interest = 0;

      expected_same_month_due_principal += emiData.principalCovered;

      if (isDelayed || !isFullPay) {
        expected_same_month_due_interest += emiData.interestCalculate ?? 0;

        // Delayed and full paid
        if (isFullPay) {
          paid_principal += emiData.principalCovered;
          paid_interest += emiData.fullPayInterest;
        }
      } else if (isFullPay) {
        expected_same_month_due_interest += emiData.fullPayInterest ?? 0;
        paid_principal += emiData.principalCovered;
        paid_interest += emiData.fullPayInterest;
      }

      const paymentData: TransactionEntity[] =
        data.currentMonthPaymentData[emiData.id] ?? [];

      paymentData.forEach((el) => {
        paid_principal += el.principalAmount ?? 0;
        paid_interest += el.interestAmount ?? 0;
      });

      paid_same_month_due_principal += paid_principal;
    }

    return {
      expected_same_month_due_principal,
      paid_same_month_due_principal,

      expected_same_month_due_interest,

      carry_forward_od_principal_paid: data.carry_forward_od_principal_paid,
      carry_forward_od_interest_paid: data.carry_forward_od_interest_paid,
    };
  }

  async csePerformanceReport(reqData) {
    const startDate = reqData?.startDate;
    const endDate = reqData?.endDate;
    if (!startDate) raiseParamMissing('startDate');
    if (!endDate) raiseParamMissing('endDate');
    const needRawData = reqData?.needRawData === true;
    const needDownload = reqData?.needDownload === true;
    const { minRange, maxRange } = this.dateService.utcDateRange(
      startDate,
      endDate,
    );
    const allDataForReport = await this.query.dataForCsePerformanceReport({
      minRange,
      maxRange,
      needRawData,
    });
    let finalResult;
    if (needRawData)
      finalResult = await this.makeCsePerformanceRawData(allDataForReport);
    else
      finalResult = await this.makeCsePerformanceSummaryData(allDataForReport);
    if (finalResult?.count == 0) raiseNotFound();

    if (!needDownload) return finalResult;
    const rawExcelData = {
      sheets: ['cse-performance-reports'],
      data: [finalResult.rows],
      sheetName: 'CSE Performance Report.xlsx',
    };
    const fileURL = await this.fileService.objectToExcelURL(rawExcelData);
    return { fileURL };
  }

  private async makeCsePerformanceRawData(reqData) {
    const { adminData, loanData, crmData } = reqData;
    const formattedLoanData = {};
    const allLoanIds = [];
    //format Loan Data
    loanData.forEach((loan) => {
      loan.loanRejectDate = loan.loanRejectDateTime
        ? this.dateService.dateToJsonStr(loan.loanRejectDateTime)
        : null;
      loan.loanDisbursementDate = loan.loanDisbursementDateTime
        ? this.dateService.dateToJsonStr(loan.loanDisbursementDateTime)
        : null;
      formattedLoanData[loan.id] = loan;
      allLoanIds.push(loan.id);
    });
    //format Admin Data
    const formattedAdminData = {};
    adminData.forEach((admin, idx) => {
      formattedAdminData[admin.id] = admin.fullName;
      if (idx == adminData.length - 1)
        formattedAdminData[SYSTEM_ADMIN_ID] = kSystem;
    });

    const resultArr = [];

    //loan id where count in admin bucket(disbursed or declined)
    let adminLoanIds = [];
    for (const crm of crmData) {
      const tempObj = {
        'Loan Id': '-',
        'User Name': '-',
        'Mobile Number': '-',
        'Completed Loan': '-',
        'CRM Date': '-',
        'CRM By': '-',
        'Reject By': '-',
        'Reject Date': '-',
        'Loan Amount': '-',
        'Loan Approved By': '-',
        'Disbursement Date': '-',
      };
      const loan = formattedLoanData[crm.loanId];
      if (!loan || !crm.createdAt) continue;
      tempObj['Loan Id'] = loan.id;
      tempObj['User Name'] = loan.fullName ?? '-';
      tempObj['Mobile Number'] = loan.phone ? decryptPhone(loan.phone) : '-';
      tempObj['Completed Loan'] = loan.completedLoan ? loan.completedLoan : '-';
      tempObj['CRM Date'] =
        this.dateService.dateToJsonStr(new Date(crm.createdDate)) ?? '-';
      tempObj['CRM By'] = formattedAdminData[crm.adminId] ?? kSystem;
      if (loan.loanStatus == 'Rejected') {
        if (
          crm.createdAt >= loan.loanRejectDateTime ||
          !loan.loanRejectDateTime
        )
          continue;
        else if (crm.createdDate == loan.loanRejectDate) {
          tempObj['Reject By'] =
            formattedAdminData[loan.manualVerificationAcceptId] ?? kSystem;
          tempObj['Reject Date'] = loan.loanRejectDate;
        }
      } else if (
        loan?.loanStatus == 'Active' ||
        loan?.loanStatus == 'Complete'
      ) {
        if (
          crm.createdAt >= loan.loanDisbursementDateTime ||
          !loan.loanDisbursementDateTime
        )
          continue;
        else if (crm.createdDate == loan.loanDisbursementDate) {
          tempObj['Loan Amount'] = loan.netApprovedAmount ?? '-';
          tempObj['Loan Approved By'] =
            formattedAdminData[loan.manualVerificationAcceptId] ?? kSystem;
          tempObj['Disbursement Date'] = loan.loanDisbursementDate;
        }
      }

      resultArr.push(tempObj);
      if (
        loan?.loanStatus == 'Accepted' ||
        loan?.loanStatus == 'InProcess' ||
        crm.createdDate == loan.loanRejectDate ||
        crm.createdDate == loan.loanDisbursementDate
      )
        adminLoanIds.push(loan.id);
    }
    adminLoanIds = [...new Set(adminLoanIds)];

    //loan id where count in system bucket(disbursed or declined)
    const systemLoanId = allLoanIds.filter((id) => !adminLoanIds.includes(id));

    for (const id of systemLoanId) {
      const loan = formattedLoanData[id];
      if (!loan.loanDisbursementDateTime && !loan.loanRejectDateTime) continue;
      const tempObj = {
        'Loan Id': '-',
        'User Name': '-',
        'Mobile Number': '-',
        'Completed Loan': '-',
        'CRM Date': '-',
        'CRM By': kSystem,
        'Reject By': '-',
        'Reject Date': '-',
        'Loan Amount': '-',
        'Loan Approved By': '-',
        'Disbursement Date': '-',
      };
      tempObj['Loan Id'] = id;
      tempObj['User Name'] = formattedLoanData[id]?.fullName ?? '-';
      tempObj['Mobile Number'] = formattedLoanData[id]?.phone
        ? decryptPhone(formattedLoanData[id]?.phone)
        : '-';
      tempObj['Completed Loan'] = formattedLoanData[id]?.completedLoan
        ? formattedLoanData[id]?.completedLoan
        : '-';

      if (loan?.loanStatus == 'Rejected') {
        tempObj['Reject By'] =
          formattedAdminData[
            formattedLoanData[id]?.manualVerificationAcceptId
          ] ?? '-';
        tempObj['Reject Date'] = formattedLoanData[id]?.loanRejectDate ?? '-';
      } else {
        tempObj['Loan Amount'] =
          formattedLoanData[id]?.netApprovedAmount ?? '-';
        tempObj['Loan Approved By'] =
          formattedAdminData[
            formattedLoanData[id]?.manualVerificationAcceptId
          ] ?? '-';
        tempObj['Disbursement Date'] =
          formattedLoanData[id]?.loanDisbursementDate ?? '-';
      }
      resultArr.push(tempObj);
    }
    //sort result
    resultArr.sort((a, b) => {
      let date1: any = '-';
      if (a['CRM Date'] != '-') date1 = a['CRM Date'];
      else if (a['Disbursement Date'] != '-') date1 = a['Disbursement Date'];
      else if (a['Reject Date'] != '-') date1 = a['Reject Date'];
      if (date1 != '-') date1 = new Date(date1).getTime();

      let date2: any = '-';
      if (b['CRM Date'] != '-') date2 = b['CRM Date'];
      else if (b['Disbursement Date'] != '-') date2 = b['Disbursement Date'];
      else if (b['Reject Date'] != '-') date2 = b['Reject Date'];
      if (date2 != '-') date2 = new Date(date2).getTime();

      if (date1 == '-' && date2 == '-') return 0;
      if (date1 == '-') return 1;
      if (date2 == '-') return -1;
      return date1 - date2;
    });
    return { count: resultArr.length, rows: resultArr };
  }

  async aadhaarStateCollection(reqData) {
    const due_loan_data =
      await this.query.dataForOverallAadhaarStateCollection(reqData);

    const total_loan_data =
      await this.query.dataForTotalLoansForAadhaarStateCollection(reqData);

    // Mapping -> "total_loan_data" into "due_loan_data"
    const finalizedList = [];
    for (let index = 0; index < due_loan_data.length; index++) {
      const value = due_loan_data[index];
      const aadhaarState = value['Aadhaar State'];

      const mappedData = total_loan_data.find(
        (el) => el.aadhaarState == aadhaarState,
      );
      if (mappedData) {
        finalizedList.push({
          'Aadhaar State': value['Aadhaar State'],
          'Total Loans': +mappedData['Total Loans'],
          'Total Due Loans': this.numService.withCommas(
            value['Total Due Loans'],
          ),
          'Total Paid Loans': this.numService.withCommas(
            +mappedData['Paid Loans'],
          ),
          'Expected EMI Amount': this.numService.withCommas(
            value['Expected EMI Amount'],
            true,
          ),
          'PAID EMI Amount': this.numService.withCommas(
            value['PAID EMI Amount'],
            true,
          ),
          'Overall Collection Efficiency (%)':
            value['Overall Collection Efficiency (%)'],
          'Collection Efficiency (0 DPD)':
            value['Collection Efficiency (0 DPD)'],
          'Collection Efficiency (1–30 DPD)':
            value['Collection Efficiency (1–30 DPD)'],
          'Collection Efficiency (31–90 DPD)':
            value['Collection Efficiency (31–90 DPD)'],
          'Collection Efficiency (90+ DPD)':
            value['Collection Efficiency (90+ DPD)'],
        });
      }
    }

    return { count: finalizedList.length, rows: finalizedList };
  }

  private async makeCsePerformanceSummaryData(reqData) {
    const { adminData: cseAdminData, loanData, crmData } = reqData;
    const formattedLoanData = {};
    const allLoanIds = [];
    //format Loan Data
    loanData.forEach((loan) => {
      loan.loanRejectDate = loan.loanRejectDateTime
        ? this.dateService.dateToJsonStr(loan.loanRejectDateTime, 'YYYY-MM-DD')
        : null;
      loan.loanDisbursementDate = loan.loanDisbursementDateTime
        ? this.dateService.dateToJsonStr(
            loan.loanDisbursementDateTime,
            'YYYY-MM-DD',
          )
        : null;
      formattedLoanData[loan.id] = loan;
      allLoanIds.push(loan.id);
    });

    //format Admin Data
    const formattedAdminData = {
      [SYSTEM_ADMIN_ID]: {
        fullName: kSystem,
        inProgress: 0,
        declined: 0,
        disbursed: 0,
        loanAmount: 0,
      },
    };
    cseAdminData.forEach((admin) => {
      formattedAdminData[admin.id] = {
        fullName: admin.fullName,
        inProgress: 0,
        declined: 0,
        disbursed: 0,
        loanAmount: 0,
      };
    });

    //loan id where count in admin bucket(disbursed or declined)
    let adminLoanIds = [];
    for (const crm of crmData) {
      const loan = formattedLoanData[crm.loanId];
      if (!loan || !crm.createdAt) continue;
      else if (
        loan?.loanStatus == 'Accepted' ||
        loan?.loanStatus == 'InProcess'
      )
        formattedAdminData[crm.adminId].inProgress++;
      else if (loan.loanStatus == 'Rejected') {
        if (
          crm.createdAt >= loan.loanRejectDateTime ||
          !loan.loanRejectDateTime
        )
          continue;
        else if (crm.createdDate == loan.loanRejectDate)
          formattedAdminData[crm.adminId].declined++;
        else formattedAdminData[crm.adminId].inProgress++;
      } else {
        if (
          crm.createdAt >= loan.loanDisbursementDateTime ||
          !loan.loanDisbursementDateTime
        )
          continue;
        else if (crm.createdDate == loan.loanDisbursementDate) {
          formattedAdminData[crm.adminId].disbursed++;
          formattedAdminData[crm.adminId].loanAmount += +loan.netApprovedAmount;
        } else formattedAdminData[crm.adminId].inProgress++;
      }

      if (
        loan?.loanStatus == 'Accepted' ||
        loan?.loanStatus == 'InProcess' ||
        crm.createdDate == loan.loanRejectDate ||
        crm.createdDate == loan.loanDisbursementDate
      )
        adminLoanIds.push(loan.id);
    }
    adminLoanIds = [...new Set(adminLoanIds)];

    //loan id where count in system bucket(disbursed or declined)
    const systemLoanId = allLoanIds.filter((id) => !adminLoanIds.includes(id));
    systemLoanId.forEach((id) => {
      const loan = formattedLoanData[id];
      if (loan?.loanStatus == 'Rejected' && loan.loanRejectDateTime)
        formattedAdminData[SYSTEM_ADMIN_ID].declined++;
      else if (
        (loan?.loanStatus == 'Active' || loan?.loanStatus == 'Complete') &&
        loan.loanDisbursementDateTime
      ) {
        formattedAdminData[SYSTEM_ADMIN_ID].disbursed++;
        formattedAdminData[SYSTEM_ADMIN_ID].loanAmount +=
          +loan.netApprovedAmount;
      }
    });

    //prepare result
    const resultArr = [];
    for (const adminId in formattedAdminData) {
      const inProgressLead = formattedAdminData[adminId]?.inProgress;
      const declinedLead = formattedAdminData[adminId]?.declined;
      const disbursedLead = formattedAdminData[adminId]?.disbursed;
      const totalLeads = inProgressLead + declinedLead + disbursedLead;
      const totalAmount = formattedAdminData[adminId].loanAmount;
      const tempObj = {
        'Full Name': formattedAdminData[adminId]?.fullName,
        'Total Leads': totalLeads ? totalLeads : '-',
        'In Progress': inProgressLead ? inProgressLead : '-',
        Declined: declinedLead ? declinedLead : '-',
        Disbursed: disbursedLead ? disbursedLead : '-',
        Amount: totalAmount ? totalAmount : '-',
        Conversion: '0',
      };
      const conversion = disbursedLead > 0 ? totalLeads / disbursedLead : 0;
      tempObj.Conversion = conversion.toFixed(2) + kPercent;
      resultArr.push(tempObj);
    }
    //sort by fullName
    resultArr.sort((a, b) => a['Full Name'].localeCompare(b['Full Name']));
    return { count: resultArr.length, rows: resultArr };
  }
}
