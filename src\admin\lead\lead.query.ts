// Imports
import { Includeable } from 'sequelize';
import { Injectable } from '@nestjs/common';
import { decryptPhone } from 'src/utils/crypt';
import { EnumService } from 'src/utils/enum.service';
import { PgService } from 'src/database/pg/pg.service';
import { NumberService } from 'src/utils/number.service';
import { MasterEntity } from 'src/database/pg/entities/master.entity';
import { registeredUsers } from 'src/database/pg/entities/registeredUsers';
import { ClickHouseService } from 'src/database/clickhouse/clickhouse.service';
import { DateService } from 'src/utils/date.service';

@Injectable()
export class LeadQuery {
  constructor(
    private readonly clickhouse: ClickHouseService,
    private readonly pg: PgService,
    private readonly dateService: DateService,
    private readonly enumService: EnumService,
    private readonly numService: NumberService,
  ) {}

  async dataForHighLeadScore(reqData) {
    let admin_where_clause = '';
    if (reqData.adminId && reqData.adminId != '-1') {
      admin_where_clause = `"recent_cse_id" = '${reqData.adminId}'`;
    } else if (reqData.adminId == '-1') {
      admin_where_clause = `"recent_cse_id" IS NOT NULL`;
    }

    const where_clause = `WHERE ${admin_where_clause} 
      AND "stage" NOT IN ('1', '2', '4', '16', '17', '19', '21') AND "lead_score" > 15`;

    // Count only
    if (reqData.isCount == true) {
      const count_query = `SELECT COUNT("user_id") AS "user_count"
          FROM "user_details" 
          
          ${where_clause}`;

      const count_data = await this.clickhouse.injectQuery(count_query);
      return +count_data[0]['user_count'];
    }
    // Raw data
    else {
      const raw_query = `SELECT "user_details"."user_id", "lead_score" AS "Lead Score",
      "stage", "enc_phone", "last_online_time" AS "Last Online Time", "crm_details"."remark" AS "Last CRM"
      FROM "user_details"

      LEFT JOIN "crm_details" ON "crm_details"."crm_id" = "user_details"."recent_crm_id"

      ${where_clause}
      
      ORDER BY "lead_score" DESC, "unique_id" DESC`;
      const raw_data: any = await this.clickhouse.injectQuery(raw_query);

      const userIds = raw_data.map((el) => el.user_id);
      const masterInc: Includeable = { model: MasterEntity };
      masterInc.attributes = ['otherInfo'];
      const userList = await this.pg.findAll(registeredUsers, {
        attributes: [
          'completedLoans',
          'fullName',
          'id',
          'lastCrm',
          'lastOnlineTime',
        ],
        include: [masterInc],
        where: { id: userIds },
      });
      const userMap = {};
      userList.forEach((el) => {
        userMap[el.id] = el;
      });

      const finzalied_list = [];
      for (let index = 0; index < raw_data.length; index++) {
        const data = raw_data[index];

        data.Phone = decryptPhone(data.enc_phone);
        delete data.enc_phone;

        const userData: registeredUsers = userMap[data.user_id] ?? {};
        const otherInfo = userData.masterData?.otherInfo ?? { salaryInfo: '0' };
        const userEnteredSalary = +(otherInfo?.salaryInfo ?? '0');
        const last_crm_data = userData.lastCrm ?? {};

        let lastActiveAgo: any = '';
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = new Date(userData?.lastOnlineTime);
          lastActiveAgoMinutes = this.dateService.difference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          lastActiveAgo =
            this.dateService.minutesToFormattedStr(lastActiveAgoMinutes);
        }

        finzalied_list.push({
          user_id: data.user_id,
          Phone: data.Phone,
          Name: userData.fullName ?? '-',
          'Lead Score': data['Lead Score'],
          Salary: this.numService.withCommas(userEnteredSalary, true),
          'Completed loans': userData.completedLoans ?? 0,
          'Action track': this.enumService.getUserStage(data.stage),
          'Last Online Time': data['Last Online Time'],
          'CRM remark': last_crm_data.remark ?? '-',
          'CRM date': last_crm_data.createdAt
            ? last_crm_data.createdAt.substring(0, 10)
            : '-',
          'CRM created by': last_crm_data.adminName ?? '-',
          isOnline: lastActiveAgoMinutes < 5,
          'Last Active ago': lastActiveAgo,
        });
      }

      return finzalied_list;
    }
  }

  async dataForHighLeadScoreUnTouched(reqData) {
    let admin_where_clause = '';
    if (reqData.adminId && reqData.adminId != '-1') {
      admin_where_clause = `"recent_cse_id" = '${reqData.adminId}'`;
    } else if (reqData.adminId == '-1') {
      admin_where_clause = `"recent_cse_id" IS NOT NULL`;
    }

    const where_clause = `WHERE ${admin_where_clause} 
      AND "stage" NOT IN ('1', '2', '4', '16', '17', '19', '21') 
      AND "lead_score" > 15 
      AND ("crm_details"."crm_id" = '' OR parseDateTimeBestEffortOrNull("crm_details"."created_at") < today())`;

    // Count Data
    if (reqData.isCount == true) {
      const count_query = `SELECT COUNT("user_details"."user_id") AS "user_count"
      FROM "user_details" 
      
      LEFT JOIN "crm_details" 
      ON "crm_details"."crm_id" = "user_details"."recent_crm_id"
      
      ${where_clause}`;

      const count_data = await this.clickhouse.injectQuery(count_query);
      return +count_data[0]['user_count'];
    }
    // Raw Data
    else {
      const raw_query = `SELECT "user_details"."user_id", "lead_score" AS "Lead Score",
      "stage", "enc_phone", "last_online_time" AS "Last Online Time", "crm_details"."remark" AS "Last CRM"
      FROM "user_details"

      LEFT JOIN "crm_details" ON "crm_details"."crm_id" = "user_details"."recent_crm_id"

      ${where_clause}
      
      ORDER BY "lead_score" DESC, "unique_id" DESC`;
      const raw_data: any = await this.clickhouse.injectQuery(raw_query);

      const userIds = raw_data.map((el) => el.user_id);
      const masterInc: Includeable = { model: MasterEntity };
      masterInc.attributes = ['otherInfo'];
      const userList = await this.pg.findAll(registeredUsers, {
        attributes: [
          'completedLoans',
          'fullName',
          'id',
          'lastCrm',
          'lastOnlineTime',
        ],
        include: [masterInc],
        where: { id: userIds },
      });
      const userMap = {};
      userList.forEach((el) => {
        userMap[el.id] = el;
      });

      const finzalied_list = [];
      for (let index = 0; index < raw_data.length; index++) {
        const data = raw_data[index];

        data.Phone = decryptPhone(data.enc_phone);
        delete data.enc_phone;

        const userData: registeredUsers = userMap[data.user_id] ?? {};
        const otherInfo = userData.masterData?.otherInfo ?? { salaryInfo: '0' };
        const userEnteredSalary = +(otherInfo?.salaryInfo ?? '0');
        const last_crm_data = userData.lastCrm ?? {};

        let lastActiveAgo: any = '';
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = new Date(userData?.lastOnlineTime);
          lastActiveAgoMinutes = this.dateService.difference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          lastActiveAgo =
            this.dateService.minutesToFormattedStr(lastActiveAgoMinutes);
        }

        finzalied_list.push({
          user_id: data.user_id,
          Phone: data.Phone,
          Name: userData.fullName ?? '-',
          'Lead Score': data['Lead Score'],
          Salary: this.numService.withCommas(userEnteredSalary, true),
          'Completed loans': userData.completedLoans ?? 0,
          'Action track': this.enumService.getUserStage(data.stage),
          'Last Online Time': data['Last Online Time'],
          'CRM remark': last_crm_data.remark ?? '-',
          'CRM date': last_crm_data.createdAt
            ? last_crm_data.createdAt.substring(0, 10)
            : '-',
          'CRM created by': last_crm_data.adminName ?? '-',
          isOnline: lastActiveAgoMinutes < 5,
          'Last Active ago': lastActiveAgo,
        });
      }

      return finzalied_list;
    }
  }

  async dataForHighLeadScoreNeverTouched(reqData) {
    let admin_where_clause = '';
    if (reqData.adminId && reqData.adminId != '-1') {
      admin_where_clause = `"recent_cse_id" = '${reqData.adminId}'`;
    } else if (reqData.adminId == '-1') {
      admin_where_clause = `"recent_cse_id" IS NOT NULL`;
    }

    const where_clause = `WHERE ${admin_where_clause} 
      AND "stage" NOT IN ('1', '2', '4', '16', '17', '19', '21') 
      AND "lead_score" > 15 
      AND ("crm_details"."crm_id" = '')`;

    // Count Data
    if (reqData.isCount == true) {
      const count_query = `SELECT COUNT("user_details"."user_id") AS "user_count"
      FROM "user_details" 
      
      LEFT JOIN "crm_details" 
      ON "crm_details"."crm_id" = "user_details"."recent_crm_id"
      
      ${where_clause}`;

      const count_data = await this.clickhouse.injectQuery(count_query);
      return +count_data[0]['user_count'];
    }
    // Raw Data
    else {
      const raw_query = `SELECT "user_details"."user_id", "lead_score" AS "Lead Score",
      "stage", "enc_phone", "last_online_time" AS "Last Online Time", "crm_details"."remark" AS "Last CRM"
      FROM "user_details"

      LEFT JOIN "crm_details" ON "crm_details"."crm_id" = "user_details"."recent_crm_id"

      ${where_clause}
      
      ORDER BY "lead_score" DESC, "unique_id" DESC`;
      const raw_data: any = await this.clickhouse.injectQuery(raw_query);

      const userIds = raw_data.map((el) => el.user_id);
      const masterInc: Includeable = { model: MasterEntity };
      masterInc.attributes = ['otherInfo'];
      const userList = await this.pg.findAll(registeredUsers, {
        attributes: [
          'completedLoans',
          'fullName',
          'id',
          'lastCrm',
          'lastOnlineTime',
        ],
        include: [masterInc],
        where: { id: userIds },
      });
      const userMap = {};
      userList.forEach((el) => {
        userMap[el.id] = el;
      });

      const finzalied_list = [];
      for (let index = 0; index < raw_data.length; index++) {
        const data = raw_data[index];

        data.Phone = decryptPhone(data.enc_phone);
        delete data.enc_phone;

        const userData: registeredUsers = userMap[data.user_id] ?? {};
        const otherInfo = userData.masterData?.otherInfo ?? { salaryInfo: '0' };
        const userEnteredSalary = +(otherInfo?.salaryInfo ?? '0');
        const last_crm_data = userData.lastCrm ?? {};

        let lastActiveAgo: any = '';
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = new Date(userData?.lastOnlineTime);
          lastActiveAgoMinutes = this.dateService.difference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          lastActiveAgo =
            this.dateService.minutesToFormattedStr(lastActiveAgoMinutes);
        }

        finzalied_list.push({
          user_id: data.user_id,
          Phone: data.Phone,
          Name: userData.fullName ?? '-',
          'Lead Score': data['Lead Score'],
          Salary: this.numService.withCommas(userEnteredSalary, true),
          'Completed loans': userData.completedLoans ?? 0,
          'Action track': this.enumService.getUserStage(data.stage),
          'Last Online Time': data['Last Online Time'],
          'CRM remark': last_crm_data.remark ?? '-',
          'CRM date': last_crm_data.createdAt
            ? last_crm_data.createdAt.substring(0, 10)
            : '-',
          'CRM created by': last_crm_data.adminName ?? '-',
          isOnline: lastActiveAgoMinutes < 5,
          'Last Active ago': lastActiveAgo,
        });
      }

      return finzalied_list;
    }
  }

  async dataForLeftTheAppFewMinsAgo(reqData) {
    let admin_where_clause = '';
    if (reqData.adminId && reqData.adminId != '-1') {
      admin_where_clause = `"recent_cse_id" = '${reqData.adminId}'`;
    } else if (reqData.adminId == '-1') {
      admin_where_clause = `"recent_cse_id" IS NOT NULL`;
    }

    const where_clause = `WHERE ${admin_where_clause}
      AND "lead_score" IS NOT NULL
      AND "stage" NOT IN ('1', '2', '4', '16', '17', '19', '21') 
      AND parseDateTimeBestEffortOrNull("last_online_time") >= now() - interval 25 minute
      AND parseDateTimeBestEffortOrNull("last_online_time") <= now() - interval 5 minute`;

    // Count Data
    if (reqData.isCount == true) {
      const count_query = `SELECT COUNT("user_id") AS "user_count"
      FROM "user_details" 
      ${where_clause}`;

      const count_data = await this.clickhouse.injectQuery(count_query);
      return +count_data[0]['user_count'];
    }
    // Raw Data
    else {
      const raw_query = `SELECT "user_details"."user_id", "lead_score" AS "Lead Score",
      "stage", "enc_phone", "last_online_time" AS "Last Online Time", "crm_details"."remark" AS "Last CRM"
      FROM "user_details"

      LEFT JOIN "crm_details" ON "crm_details"."crm_id" = "user_details"."recent_crm_id"

      ${where_clause}
      
      ORDER BY "lead_score" DESC, "unique_id" DESC`;
      const raw_data: any = await this.clickhouse.injectQuery(raw_query);

      const userIds = raw_data.map((el) => el.user_id);
      const masterInc: Includeable = { model: MasterEntity };
      masterInc.attributes = ['otherInfo'];
      const userList = await this.pg.findAll(registeredUsers, {
        attributes: [
          'completedLoans',
          'fullName',
          'id',
          'lastCrm',
          'lastOnlineTime',
        ],
        include: [masterInc],
        where: { id: userIds },
      });
      const userMap = {};
      userList.forEach((el) => {
        userMap[el.id] = el;
      });

      const finzalied_list = [];
      for (let index = 0; index < raw_data.length; index++) {
        const data = raw_data[index];

        data.Phone = decryptPhone(data.enc_phone);
        delete data.enc_phone;

        const userData: registeredUsers = userMap[data.user_id] ?? {};
        const otherInfo = userData.masterData?.otherInfo ?? { salaryInfo: '0' };
        const userEnteredSalary = +(otherInfo?.salaryInfo ?? '0');
        const last_crm_data = userData.lastCrm ?? {};

        let lastActiveAgo: any = '';
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = new Date(userData?.lastOnlineTime);
          lastActiveAgoMinutes = this.dateService.difference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          lastActiveAgo =
            this.dateService.minutesToFormattedStr(lastActiveAgoMinutes);
        }

        finzalied_list.push({
          user_id: data.user_id,
          Phone: data.Phone,
          Name: userData.fullName ?? '-',
          'Lead Score': data['Lead Score'],
          Salary: this.numService.withCommas(userEnteredSalary, true),
          'Completed loans': userData.completedLoans ?? 0,
          'Action track': this.enumService.getUserStage(data.stage),
          'Last Online Time': data['Last Online Time'],
          'CRM remark': last_crm_data.remark ?? '-',
          'CRM date': last_crm_data.createdAt
            ? last_crm_data.createdAt.substring(0, 10)
            : '-',
          'CRM created by': last_crm_data.adminName ?? '-',
          isOnline: lastActiveAgoMinutes < 5,
          'Last Active ago': lastActiveAgo,
        });
      }

      return finzalied_list;
    }
  }

  async dataForNthTimeAttemptRemain(reqData) {
    let admin_where_clause = '';
    if (reqData.adminId && reqData.adminId != '-1') {
      admin_where_clause = `AND "recent_cse_id" = '${reqData.adminId}'`;
    } else if (reqData.adminId == '-1') {
      admin_where_clause = `AND "recent_cse_id" IS NOT NULL`;
    }

    // Count Data
    if (reqData.isCount == true) {
      const count_query = `
      WITH
        -- Step 1: Filter today's CRM in IST
        crm_today AS (
            SELECT
                crm_id,
                user_id,
                remark,
                toTimeZone(parseDateTimeBestEffort(created_at), 'Asia/Kolkata') AS created_ist
            FROM crm_details
            WHERE
                toTimeZone(parseDateTimeBestEffort(created_at), 'Asia/Kolkata') >= toStartOfDay(now(), 'Asia/Kolkata')
                AND toTimeZone(parseDateTimeBestEffort(created_at), 'Asia/Kolkata') < toStartOfDay(now() + INTERVAL 1 DAY, 'Asia/Kolkata')
        ),

        -- Step 2: Find user_ids with exactly N CRM entries today
        valid_crm_users AS ( SELECT user_id
            FROM crm_today
            GROUP BY user_id HAVING count() = ${reqData.crm_attempt}
        )

      -- Step 3: Count users with valid CRM today
      SELECT count(*) AS user_count
      FROM user_details u
      INNER JOIN crm_today c ON c.crm_id = u.recent_crm_id
      INNER JOIN valid_crm_users v ON v.user_id = u.user_id
      WHERE
          u.recent_cse_id IS NOT NULL
          AND u.stage NOT IN ('1', '2', '4', '16', '17', '19', '21') 
          ${admin_where_clause};`;

      const count_data = await this.clickhouse.injectQuery(count_query);
      return +count_data[0]['user_count'];
    }
    // Raw Data
    else {
      const raw_query = `WITH
          -- Step 1: Filter today's CRM in IST
          crm_today AS (
              SELECT
                  crm_id,
                  user_id,
                  remark,
                  toTimeZone(parseDateTimeBestEffort(created_at), 'Asia/Kolkata') AS created_ist
              FROM crm_details
              WHERE
                  created_ist >= toStartOfDay(now(), 'Asia/Kolkata')
                  AND created_ist < toStartOfDay(now() + INTERVAL 1 DAY, 'Asia/Kolkata')),
          
          -- Step 2: Find user_ids with exactly one CRM today
          valid_crm_users AS (
              SELECT user_id FROM crm_today
              GROUP BY user_id HAVING count() = ${reqData.crm_attempt} )
          
      -- Step 3: Join back to get full info for users with exactly one CRM today
      SELECT
          u.user_id AS "user_id",
          u.lead_score AS "Lead Score",
          u.stage,
          u.enc_phone,
          u.last_online_time AS "Last Online Time",
          c.remark AS "Last CRM"
      FROM user_details u
      INNER JOIN crm_today c ON c.crm_id = u.recent_crm_id
      INNER JOIN valid_crm_users v ON v.user_id = u.user_id
      WHERE
          u.recent_cse_id IS NOT NULL
          AND u.stage NOT IN ('1', '2', '4', '16', '17', '19', '21')
          ${admin_where_clause}
      ORDER BY u.lead_score DESC, u.unique_id DESC`;

      const raw_data: any = await this.clickhouse.injectQuery(raw_query);

      const userIds = raw_data.map((el) => el.user_id);
      const masterInc: Includeable = { model: MasterEntity };
      masterInc.attributes = ['otherInfo'];
      const userList = await this.pg.findAll(registeredUsers, {
        attributes: [
          'completedLoans',
          'fullName',
          'id',
          'lastCrm',
          'lastOnlineTime',
        ],
        include: [masterInc],
        where: { id: userIds },
      });
      const userMap = {};
      userList.forEach((el) => {
        userMap[el.id] = el;
      });

      const finzalied_list = [];
      for (let index = 0; index < raw_data.length; index++) {
        const data = raw_data[index];

        data.Phone = decryptPhone(data.enc_phone);
        delete data.enc_phone;

        const userData: registeredUsers = userMap[data.user_id] ?? {};
        const otherInfo = userData.masterData?.otherInfo ?? { salaryInfo: '0' };
        const userEnteredSalary = +(otherInfo?.salaryInfo ?? '0');
        const last_crm_data = userData.lastCrm ?? {};

        let lastActiveAgo: any = '';
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = new Date(userData?.lastOnlineTime);
          lastActiveAgoMinutes = this.dateService.difference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          lastActiveAgo =
            this.dateService.minutesToFormattedStr(lastActiveAgoMinutes);
        }

        finzalied_list.push({
          user_id: data.user_id,
          Phone: data.Phone,
          Name: userData.fullName ?? '-',
          'Lead Score': data['Lead Score'],
          Salary: this.numService.withCommas(userEnteredSalary, true),
          'Completed loans': userData.completedLoans ?? 0,
          'Action track': this.enumService.getUserStage(data.stage),
          'Last Online Time': data['Last Online Time'],
          'CRM remark': last_crm_data.remark ?? '-',
          'CRM date': last_crm_data.createdAt
            ? last_crm_data.createdAt.substring(0, 10)
            : '-',
          'CRM created by': last_crm_data.adminName ?? '-',
          isOnline: lastActiveAgoMinutes < 5,
          'Last Active ago': lastActiveAgo,
        });
      }

      return finzalied_list;
    }
  }
}
