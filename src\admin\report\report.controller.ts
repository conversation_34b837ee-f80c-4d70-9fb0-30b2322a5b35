// Imports
import { ReportService } from './report.service';
import { Body, Controller, Post } from '@nestjs/common';

@Controller('admin/report')
export class ReportController {
  constructor(private readonly service: ReportService) {}

  @Post('collectionEfficiency')
  async funCollectionEfficiency(@Body() body) {
    return await this.service.collectionEfficiency(body);
  }

  @Post('csePerformanceReport')
  async funCsePerformanceReport(@Body() body) {
    return await this.service.csePerformanceReport(body);
  }

  @Post('aadhaarStateCollection')
  async funAadhaarStateCollection(@Body() body) {
    return await this.service.aadhaarStateCollection(body);
  }
}
