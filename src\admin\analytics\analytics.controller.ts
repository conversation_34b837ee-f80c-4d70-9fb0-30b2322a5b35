import { Controller, Get, Query } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { UserQueryDto } from './entity/dto/userQuery.dto';

@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly service: AnalyticsService) {}

  // Dashboard API
  @Get()
  async getDashboard() {
    return await this.service.getDashboard();
  }

  // User Stage Analytics
  @Get('stage')
  async getUserStageData(@Query() query: UserQueryDto) {
    return await this.service.getUserStageData(query);
  }

  // Registered User
  @Get('register')
  async getRegisteredUserData(@Query() query: UserQueryDto) {
    return this.service.getRegisteredUserData(query);
  }
}
