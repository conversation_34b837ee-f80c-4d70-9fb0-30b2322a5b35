import { Controller, Get, Query } from '@nestjs/common';
import { AnalyticsNewService } from './analyticsNew.service';
import { UserQueryDto } from './entity/dto/userQuery.dto';

@Controller('analytics')
export class AnalyticsNewController {
  constructor(private readonly service: AnalyticsNewService) {}

  @Get('dashboard')
  async getDashboard() {
    return this.service.getDashboard();
  }

  @Get('stageNew')
  async getRegisteredUserCount(@Query() query: UserQueryDto) {
    return this.service.getUserStageData(query);
  }

  @Get('registerNew')
  async getRegistrationSummary(@Query() query: UserQueryDto) {
    return this.service.getRegistrationSummary(query);
  }
}
