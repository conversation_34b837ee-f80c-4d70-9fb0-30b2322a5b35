// Imports
import { Op } from 'sequelize';
import { Includeable } from 'sequelize';
import { Injectable } from '@nestjs/common';
import { raiseParamMissing } from 'src/config/error';
import { PgService } from 'src/database/pg/pg.service';
import { kGlobalDateTrail } from 'src/constant/global';
import { KYCEntity } from 'src/database/pg/entities/kyc.entity';
import { EmiEntity } from 'src/database/pg/entities/emi.entity';
import { BankingEntity } from 'src/database/pg/entities/banking.entity';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import { CibilScoreEntity } from 'src/database/pg/entities/cibil.score.entity';
import { TransactionEntity } from 'src/database/pg/entities/transaction.entity';
import { DateService } from 'src/utils/date.service';

@Injectable()
export class DataCodesQuery {
  constructor(
    private readonly pg: PgService,
    private readonly dateService: DateService,
  ) {}

  async dataForInternalScore(loanId) {
    const bankingInclude: Includeable = {
      attributes: ['adminSalary', 'salary'],
      model: BankingEntity,
    };

    const cibilInclude: Includeable = {
      attributes: [
        'cibilScore',
        'currentBalance',
        'overdueBalance',
        'highCreditAmount',
        'inquiryPast30Days',
        'PLAccounts',
        'PLOutstanding',
        'plScore',
        'responsedata',
        'totalOverdueDays',
        'zeroBalanceAccounts',
      ],
      model: CibilScoreEntity,
    };

    const loanData: loanTransaction = await this.pg.findOne(loanTransaction, {
      attributes: ['cibilId', 'completedLoan', 'initialTypeOfDevice', 'userId'],
      include: [bankingInclude, cibilInclude],
      where: { id: loanId },
    });

    // Separate join to avoid deep joins in one query
    const kycData = await this.pg.findOne(KYCEntity, {
      afterFindOptions: { aadhaarState: true, dobInYears: true },
      attributes: ['aadhaarState', 'aadhaarDOB'],
      order: [['id', 'DESC']],
      where: { userId: loanData.userId },
    });

    return {
      ...loanData,
      aadhaarState: kycData.aadhaarState,
      dobInYears: kycData.dobInYears,
    };
  }

  async dataForSyncEMIDetails(reqData) {
    let startDate = reqData.startDate;
    if (!startDate) {
      raiseParamMissing('startDate');
    }
    startDate = startDate + kGlobalDateTrail;
    let endDate = reqData.endDate;
    if (!endDate) {
      raiseParamMissing('endDate');
    }
    endDate = endDate + kGlobalDateTrail;

    // Gathering -> EMI data
    const emiList: EmiEntity[] = await this.pg.findAll(EmiEntity, {
      attributes: [
        'fullPayPrincipal',
        'id',
        'loanId',
        'userId',
        'emi_date',
        'emiNumber',
        'partOfemi',
        'payment_done_date',
        'pay_type',
        'principalCovered',
      ],
      where: { emi_date: { [Op.gte]: startDate, [Op.lte]: endDate } },
    });

    // Gathering -> Loan Data
    const loanIds = emiList.map((el) => el.loanId);
    const loanList = await this.pg.findAll(loanTransaction, {
      attributes: ['id', 'cibilId', 'completedLoan'],
      where: { id: loanIds },
    });
    // Converting -> Array into object for better performance of linking with EMI data during iteration
    const loanData = {};
    loanList.forEach((el) => {
      loanData[el.id] = el;
    });

    // Gathering -> Cibil Data
    let cibilIds = loanList.map((el) => el.cibilId);
    cibilIds = cibilIds.filter((el) => el);
    const cibilList = await this.pg.findAll(CibilScoreEntity, {
      attributes: ['cibilScore', 'id', 'inquiryPast30Days', 'plScore'],
      where: { id: cibilIds },
    });
    // Converting -> Array into object for better performance of linking with loan or EMIs during iteration
    const cibilData = {};
    cibilList.forEach((el) => {
      cibilData[el.id] = el;
    });

    // Gathering -> Transaction data (Payment data)
    const emiIds = emiList.map((el) => el.id);
    const transactionList = await this.pg.findAll(TransactionEntity, {
      attributes: [
        'completionDate',
        'emiId',
        'principalAmount',
        'interestAmount',
      ],
      where: { emiId: emiIds, status: 'COMPLETED' },
    });
    // Converting -> Array into object for better performance of linking with EMI data during iteration
    const transactionData: {} = {};
    transactionList.forEach((el) => {
      if (!transactionData[el.emiId]) {
        transactionData[el.emiId] = [];
      }
      transactionData[el.emiId].push(el);
    });

    return { emiList, cibilData, loanData, transactionData };
  }

  async dataForSyncCIBILDetails(reqData) {
    const { page, pageSize } = reqData;
    const offset = page * pageSize - pageSize;

    const cibilList = await this.pg.findAll(CibilScoreEntity, {
      attributes: ['id', 'userId', 'responsedata'],
      limit: pageSize,
      offset,
      order: [['id', 'DESC']],
      where: {
        createdAt: { [Op.gte]: '2024-01-01' + kGlobalDateTrail },
        status: '1',
      },
    });

    // Pre-calculate total accounts for array allocation
    let totalAccounts = 0;
    for (const cibilData of cibilList) {
      totalAccounts +=
        cibilData.responsedata.consumerCreditData[0].accounts?.length || 0;
    }

    const finalizedList = new Array(totalAccounts);
    let currentIndex = 0;
    const dateCache = new Map();

    const cachedStrToDate = (dateStr, format) => {
      if (!dateStr) return null;
      const cacheKey = `${dateStr}|${format}`;
      if (dateCache.has(cacheKey)) return dateCache.get(cacheKey);
      const date = this.dateService.strToDate(dateStr, format);
      dateCache.set(cacheKey, date);
      return date;
    };

    for (const cibilData of cibilList) {
      const consumerCreditData = cibilData.responsedata.consumerCreditData[0];
      const scores = consumerCreditData.scores ?? [];

      const scoreMap = scores.reduce((map, score) => {
        map[score.scoreName] = score;
        return map;
      }, {});

      const tusc3Data = scoreMap['CIBILTUSC3'];
      const plData = scoreMap['PLSCORE'];
      const fetch_date = cachedStrToDate(tusc3Data.scoreDate, 'DDMMYYYY');
      const accounts = consumerCreditData.accounts ?? [];

      const basicData = {
        cibil_id: cibilData.id,
        fetch_date,
        user_id: cibilData.userId,
        cibil_score: tusc3Data.score == '000-1' ? -1 : +tusc3Data.score,
        pl_score: plData.score == '000-1' ? -1 : +plData.score,
        total_accounts: accounts.length,
      };

      for (const accData of accounts) {
        const finalizedData: any = { ...basicData };

        finalizedData.open_date = cachedStrToDate(
          accData.dateOpened,
          'DDMMYYYY',
        );
        finalizedData.close_date = cachedStrToDate(
          accData.dateClosed,
          'DDMMYYYY',
        );
        finalizedData.reported_date = cachedStrToDate(
          accData.dateReported,
          'DDMMYYYY',
        );

        finalizedData.credit_amount = accData.highCreditAmount ?? 0;
        finalizedData.outstanding_amount = finalizedData.close_date
          ? 0
          : (accData.currentBalance ?? 0);
        finalizedData.account_type = accData.accountType ?? 0;
        finalizedData.overdue_amount = accData.amountOverdue ?? 0;
        finalizedData.recent_delay_days = accData.lastDelayDays ?? 0;
        finalizedData.last_6_months_delay_days = accData.past6MonDelayDays ?? 0;
        finalizedData.payment_history = accData.paymentHistory;
        finalizedData.collateral_type = accData.collateralType;
        finalizedData.collatera_value = accData.collateralValue;

        finalizedList[currentIndex++] = finalizedData;
      }
    }

    return {
      cibil_list_count: cibilList.length,
      count: finalizedList.length,
      rows: finalizedList,
    };
  }
}
