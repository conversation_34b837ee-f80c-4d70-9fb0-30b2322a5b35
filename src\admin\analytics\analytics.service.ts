import { Injectable } from '@nestjs/common';
import { ClickHouseService } from 'src/database/clickhouse/clickhouse.service';
import { RedisService } from 'src/database/redis/redis.service';
import { CreateQuery } from 'src/createTable/createQuery';
import { PgService } from 'src/database/pg/pg.service';
import { CommonService } from 'src/utils/comman.service';
import { NUMBERS, UserStage, UserStageCount } from 'src/constant/objects';
import { AnalyticsEntity } from './entity/dto/analytics.dto';
import { GraphFilterEntity } from './entity/dto/graphFilter.dto';
import { HTTPError } from 'src/config/error';
import {
  CHART_GRAPH_DATE_QUERY,
  END_AGE_CONDITION,
  QUERY_AGE_SUBQUERY_BASE,
  QUERY_GRAPHS,
  QUERY_GRAPH_FILTERS,
  QUERY_REGISTRATION_SUMMARY,
  QUERY_SCORE_SUBQUERY_BASE,
  QUERY_STAGE_COUNT,
  START_AGE_CONDITION,
} from './analytics.query';

@Injectable()
export class AnalyticsService {
  constructor(
    private readonly ClickHouseService: ClickHouseService,
    private readonly redisService: RedisService,
    private readonly querService: CreateQuery,
    private readonly commonService: CommonService,
    private readonly pgService: PgService,
  ) {}
  async getDashboard() {
    const [graphs, filters] = await Promise.all([
      this.ClickHouseService.injectQuery(QUERY_GRAPHS),
      this.ClickHouseService.injectQuery(QUERY_GRAPH_FILTERS),
    ]);

    const filtersArray = filters as any[];
    const today = new Date().toISOString().split('T')[0];
    const updatedFilters = filtersArray.map((filter) => {
      if (filter.type === 3) {
        return {
          ...filter,
          defaultValue: {
            start: today,
            end: today,
          },
        };
      }
      return filter;
    });
    return { filters: updatedFilters, graphs };
  }

  // #region user stage data in ClickHouse
  async getUserStageData(query) {
    const { startDate, endDate } = this.commonService.getUTCDateRange(
      query?.startDate,
      query?.endDate,
    );
    const todayStr = new Date().toISOString().split('T')[0];
    const flag = !!query?.flage;
    const aggregate = {};
    Object.keys(UserStage).forEach((key) => {
      aggregate[key] = 0;
    });

    const result = await this.getDateWiseAggregate({
      query,
      startDate,
      endDate,
      todayStr,
      flag,
      aggregate,
      redisKeyBase: 'USER_STAGE',
      redisCacheKey: this.buildRedisKey('USER_STAGE_FILTER', query),
      clickhouseTable: 'UsersStageAnalytics',
      getPgData: this.getStageByDate.bind(this),
      getNextId: this.querService.userStageId.bind(this.querService),
    });
    if (!result || !result.length) {
      throw HTTPError({ message: 'No user stage data found' });
    }
    return result;
  }

  //#endregion

  // #region filtered stage data from PostgreSQL (with filters)
  async getStageByDate(query) {
    const { startDate, endDate } = this.commonService.getUTCDateRange(
      query?.startDate,
      query?.endDate,
    );
    if (!startDate) throw HTTPError({ message: 'Invalid start date' });

    const conditions: string[] = [];
    if (startDate && endDate) {
      conditions.push(
        `"createdAt" >= '${startDate}' AND "createdAt" <= '${endDate}'`,
      );
    } else {
      conditions.push(`"createdAt" = '${startDate}'`);
    }
    if (query?.gender) {
      conditions.push(`gender = '${query.gender}'`);
    }
    if (query?.state) {
      const stateList = Array.isArray(query.state)
        ? query.state
        : query.state.split(',').map((s) => s.trim());
      if (stateList.length > 0) {
        const formattedStates = stateList
          .map((s) => `'${s.toUpperCase()}'`)
          .join(', ');
        conditions.push(`UPPER("state") IN (${formattedStates})`);
      }
    }
    const scoreSubquery = this.scoreSubquery(query);
    if (scoreSubquery) conditions.push(`id IN (${scoreSubquery})`);
    const ageSubquery = this.ageSubquery(query);
    if (ageSubquery) conditions.push(`id IN (${ageSubquery})`);

    const whereClause = conditions.length
      ? `WHERE ${conditions.join(' AND ')}`
      : '';
    const sql = QUERY_STAGE_COUNT.replace('{{WHERE_CLAUSE}}', whereClause);
    const rows = await this.pgService.query(sql);
    if (!rows || rows.length === 0) {
      throw HTTPError({ message: 'No data returned from database' });
    }

    const finalOb = Object.fromEntries(
      Object.keys(UserStage).map((k) => [k, 0]),
    );

    for (const elem of rows) {
      const keyName = this.getKeyByValue(elem['stage'], UserStage);
      if (keyName) finalOb[keyName] = parseInt(elem.count ?? 0, 10);
    }
    return finalOb;
  }
  //#endregion
  private async getDateWiseAggregate({
    query,
    startDate,
    endDate,
    todayStr,
    flag,
    aggregate,
    redisKeyBase,
    redisCacheKey,
    clickhouseTable,
    getPgData,
    getNextId,
  }) {
    const listData = [];
    // 1. Today Single date + flag
    if (flag && startDate === todayStr) {
      const cached = await this.redisService.getString(redisCacheKey);
      if (cached) return this.aggregateData(aggregate, [JSON.parse(cached)]);
      const pgData = await getPgData({ ...query, startDate, endDate });
      if (!pgData) {
        throw HTTPError({ message: 'No data from PostgreSQL for aggregation' });
      }
      await this.redisService.setString(
        redisCacheKey,
        JSON.stringify(pgData),
        NUMBERS.THREE_HOURS_IN_SECONDS,
      );
      return this.aggregateData(aggregate, [pgData]);
    }
    // 2. Non-today single date + flag
    if (flag && startDate !== todayStr) {
      const pgData = await getPgData({ ...query, startDate, endDate });
      if (!pgData) {
        throw HTTPError({ message: 'No data from PostgreSQL for aggregation' });
      }
      return this.aggregateData(aggregate, [pgData]);
    }
    // 3. Range + flag
    for (
      let d = new Date(startDate);
      d <= new Date(endDate);
      d.setDate(d.getDate() + 1)
    ) {
      const dateStr = d.toISOString().split('T')[0];
      // Try cache first
      if (dateStr === todayStr) {
        const cached = await this.redisService.getString(redisKeyBase);
        if (cached) {
          listData.push(JSON.parse(cached));
          continue;
        }
      }
      // Try ClickHouse
      const chQuery = CHART_GRAPH_DATE_QUERY.replace(
        '{{TABLE_NAME}}',
        clickhouseTable,
      ).replace('{{GRAPH_DATE}}', dateStr);
      const response: any = await this.ClickHouseService.injectQuery(chQuery);
      if (response?.length) {
        listData.push(...response);
        continue;
      }
      // Fallback: PostgreSQL
      const pgData = await getPgData({
        ...query,
        startDate: dateStr,
        endDate: dateStr,
      });
      if (pgData && pgData[Object.keys(pgData)[0]] > 0) {
        const nextId = await getNextId();
        const clickhouseInsert = { id: nextId, graph_date: dateStr, ...pgData };
        if (dateStr === todayStr) {
          await this.redisService.setString(
            redisKeyBase,
            JSON.stringify(clickhouseInsert),
            NUMBERS.THREE_HOURS_IN_SECONDS,
          );
        } else {
          await this.ClickHouseService.insertToClickhouse(
            clickhouseTable,
            clickhouseInsert,
          );
        }
        listData.push(clickhouseInsert);
      }
    }

    if (!listData.length) {
      throw HTTPError({
        message: 'No data found for the specified date range',
      });
    }
    return this.aggregateData(aggregate, listData);
  }

  // #region registration summary aggregation
  async getRegisteredUserData(query) {
    const startDate = query?.startDate ? new Date(query.startDate) : new Date();
    const endDate = query?.endDate ? new Date(query.endDate) : startDate;
    const todayStr = new Date().toISOString().split('T')[0];
    const currentDate = startDate.toISOString().split('T')[0];
    const flag = query?.flage ?? false;
    const aggregate = {};
    Object.values(UserStageCount).forEach((fieldName) => {
      aggregate[fieldName] = 0;
    });
    return this.getDateWiseAggregate({
      query,
      startDate: currentDate,
      endDate,
      todayStr,
      flag,
      aggregate,
      redisKeyBase: 'USER_REGISTER',
      redisCacheKey: this.buildRedisKey('USER_REGISTER_FILTER', query),
      clickhouseTable: 'RegisteredUsers',
      getPgData: this.getRegisteredUsersByDate.bind(this),
      getNextId: this.querService.registeredUserId.bind(this.querService),
    });
  }
  // #region user registration daywise filtering
  async getRegisteredUsersByDate(query) {
    const { startDate, endDate } = this.commonService.getUTCDateRange(
      query?.startDate,
      query?.endDate,
    );
    if (!startDate) throw HTTPError({ message: 'Invalid start date' });

    const conditions: string[] = [];
    if (startDate && endDate) {
      conditions.push(
        `"createdAt" >= '${startDate}' AND "createdAt" <= '${endDate}'`,
      );
    } else {
      conditions.push(`"createdAt" = '${startDate}'`);
    }
    if (query?.gender) conditions.push(`gender = '${query.gender}'`);
    if (query?.state) {
      const stateList = Array.isArray(query.state)
        ? query.state
        : query.state.split(',').map((s) => s.trim());
      if (stateList.length > 0) {
        const formattedStates = stateList
          .map((s) => `'${s.toUpperCase()}'`)
          .join(', ');
        conditions.push(`UPPER("state") IN (${formattedStates})`);
      }
    }
    const scoreSubquery = this.scoreSubquery(query);
    if (scoreSubquery) conditions.push(`id IN (${scoreSubquery})`);
    const ageSubquery = this.ageSubquery(query);
    if (ageSubquery) conditions.push(`id IN (${ageSubquery})`);

    const whereClause = conditions.length
      ? `WHERE ${conditions.join(' AND ')}`
      : '';

    const sql = QUERY_REGISTRATION_SUMMARY.replace(
      '{{WHERE_CLAUSE}}',
      whereClause,
    );
    const [result] = await this.pgService.query(sql);
    if (!result) {
      throw HTTPError({ message: 'No data found for registration summary' });
    }
    return {
      registration_count: Number(result.registration_count),
      new_users: Number(result.new_users),
      repeat_users: Number(result.repeat_users),
      male_registration: Number(result.male_registration),
      female_registration: Number(result.female_registration),
      android_users: Number(result.android_users),
      ios_users: Number(result.ios_users),
      web_users: Number(result.web_users),
    };
  }

  //#endregion

  // #region filter helpers (score)
  scoreSubquery(query) {
    const { startCibil, endCibil, startPl, endPl } = query || {};
    const scoreConditions: string[] = [];
    if (startCibil && endCibil)
      scoreConditions.push(
        `"cibilScore" >= ${startCibil} AND "cibilScore" <= ${endCibil}`,
      );
    else if (startCibil) scoreConditions.push(`"cibilScore" >= ${startCibil}`);
    else if (endCibil) scoreConditions.push(`"cibilScore" <= ${endCibil}`);
    if (startPl && endPl)
      scoreConditions.push(`"plScore" >= ${startPl} AND "plScore" <= ${endPl}`);
    else if (startPl) scoreConditions.push(`"plScore" >= ${startPl}`);
    else if (endPl) scoreConditions.push(`"plScore" <= ${endPl}`);
    if (!scoreConditions.length) return null;
    return QUERY_SCORE_SUBQUERY_BASE.replace(
      '{{SCORE_CONDITIONS}}',
      scoreConditions.join(' AND '),
    );
  }

  //#endregion

  // #region filter by the age
  ageSubquery(query) {
    const { startAge, endAge } = query || {};
    if (!startAge && !endAge) return null;

    const ageConditions: string[] = [];
    // Compose filter for all DOB string formats
    if (startAge)
      ageConditions.push(
        START_AGE_CONDITION.replace('{{START_AGE}}', startAge),
      );

    if (endAge)
      ageConditions.push(END_AGE_CONDITION.replace('{{END_AGE}}', endAge));
    return QUERY_AGE_SUBQUERY_BASE.replace(
      '{{AGE_CONDITION_CLAUSE}}',
      ageConditions.length ? ' AND ' + ageConditions.join(' AND ') : '',
    );
  }
  //#endregion

  private buildRedisKey(baseKey: string, query: any) {
    const parts: string[] = [];

    if (query.gender) parts.push(`GENDER_${query.gender.toUpperCase()}`);
    if (query.state) parts.push(`STATE_${query.state.toUpperCase()}`);
    if (query.startAge || query.endAge)
      parts.push(`AGE_${query.startAge || ''}-${query.endAge || ''}`);
    if (query.startCibil || query.endCibil || query.startPl || query.endPl) {
      parts.push(
        `SCORE_CIBIL${query.startCibil || ''}-${query.endCibil || ''}_PL${query.startPl || ''}-${query.endPl || ''}`,
      );
    }

    return parts.length > 0 ? `${baseKey}_${parts.join('_')}` : baseKey;
  }

  private aggregateData(base, dataList) {
    for (const item of dataList) {
      for (const key in base) {
        base[key] += item[key] || 0;
      }
    }
    // For consistency, return { xKey, yKey } array pattern
    return Object.entries(base).map(([xKey, yKey]) => ({
      xKey: this.commonService.toPascalCase(xKey),
      yKey,
    }));
  }

  private getKeyByValue(val, targetObj) {
    return Object.keys(targetObj).find((key) => targetObj[key] === val);
  }

  // #region graph & filter insertion utilities
  async createFilter(graph: GraphFilterEntity) {
    const nextId = await this.querService.graphfilterId();
    const data = {
      id: nextId,
      title: graph.title,
      defaultValue: graph.defaultValue,
      type: graph.type,
      otherInfo: graph.otherInfo,
      isActive: graph.isActive ?? 1,
    };
    await this.ClickHouseService.insertToClickhouse('graphFilters', data);
    return data;
  }

  async create(graph: AnalyticsEntity) {
    const nextId = await this.querService.graphId();
    const data = {
      id: nextId,
      type: graph.type,
      title: graph.title,
      description: graph.description,
      subtitle: graph.subtitle,
      row: graph.row,
      column: graph.column,
      api: graph.api,
      method: graph.method,
      isActive: graph.isActive ?? 1,
    };
    await this.ClickHouseService.insertToClickhouse('graphs', data);
    return data;
  }
  //#endregion
}
