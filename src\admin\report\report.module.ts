// Imports
import { Modu<PERSON> } from '@nestjs/common';
import { ReportService } from './report.service';
import { ReportQueryService } from './report.query';
import { PgModule } from 'src/database/pg/pg.module';
import { ReportController } from './report.controller';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  controllers: [ReportController],
  imports: [PgModule, UtilsModule],
  providers: [ReportQueryService, ReportService],
})
export class ReportModule {}
