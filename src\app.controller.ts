// Imports
import { AppService } from './app.service';
import { Request, Response } from 'express';
import { Body, Controller, Get, Post, Query, Res, Req } from '@nestjs/common';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello() {
    return this.appService.getHello();
  }

  @Post('testWebhook')
  async funTestWebhook(@Body() body, @Query() query) {
    console.log('body', body, 'query', query);

    return {};
  }

  @Get('exotelUrl')
  exotelUrl(@Res() res: Response) {
    res.setHeader('Content-Type', 'text/plain'); // ⚑ must be text/plain
    res.status(200).send('Hey there i am calling from system'); // body exactly what TTS should read
  }

  @Get('connectToAgent')
  connect(@Req() req: Request, @Res() res: Response) {
    const caller = ((req.query.From as string) || '').replace(/^0|\+/g, '');
    console.log({ query: req.query });

    const agentMap: Record<string, string> = {
      '8140549473': '+917600550021', // abc
      '7600550021': '+918200008270', // xyz
    };
    const dial = agentMap[caller] || '+917600550021';

    const payload = {
      fetch_after_attempt: false, // don’t call this URL again if agent misses
      destination: {
        // ← mandatory field :contentReference[oaicite:1]{index=1}
        numbers: [dial], // array, order = dial order
      },
      outgoing_phone_number: '+918045680015', // your Exophone CLI
      record: true,
      recording_channels: 'dual',
      max_ringing_duration: 45,
      max_conversation_duration: 3600,
      music_on_hold: { type: 'operator_tone' },
      start_call_playback: {
        playback_to: 'both',
        type: 'text',
        value: 'Connecting you to the agent',
      },
    };

    console.log('payload', payload);

    res.type('application/json').status(200).json(payload);
  }
}
