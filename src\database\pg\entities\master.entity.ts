// Imports
import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  HasOne,
} from 'sequelize-typescript';
import { registeredUsers } from './registeredUsers';
import { loanTransaction } from './loanTransaction';

@Table({})
export class MasterEntity extends Model<MasterEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: {
      // -1 indicates pending from user
      phone: -1,
      permission: -1,
      selfie: -1,
      email: -1,
      pan: -1,
      basic: -1,
      personal: -1,
      professional: -1,
      pin: -1,
      aadhaar: -1,
      company: -1,
      workMail: -1,
      salarySlip: -1,
      bank: -1, // 8 indicates user on hold
      // -2 indicates loan is not started yet, 8 indicates user on hold
      loan: -2,
      eMandate: -1,
      eSign: -1,
      disbursement: -1,
    },
  })
  status: {
    phone: number;
    permission: number;
    selfie: number;
    email: number;
    pan: number;
    basic: number;
    personal: number;
    professional: number;
    pin: number;
    aadhaar: number;
    company: number;
    workMail: number;
    salarySlip: number;
    bank: number;
    loan: number;
    eMandate: number;
    eSign: number;
    disbursement: number;
  };

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: {},
  })
  dates: {
    registration: number;
    basicDetails: number;
    professionalDetails: number;
    aadhaar: number;
    selfie: number;
    employment: number;
    banking: number;
    eligibility: number;
    eMandate: number;
    eSign: number;
    disbursement: number;
  };

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: {},
  })
  rejection: {
    aadhaar: string;
    pan: string;
    company: string;
    workMail: string;
    salarySlip: string;
    banking: string;
    eligibility: string;
    eMandate: string;
    eSign: string;
    disbursement: string;
  };

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: { purposeName: '', purposeId: 0 },
  })
  miscData: { purposeName: string; purposeId: number };

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: { count: 0, coolOffEndsOn: '', coolOffStartedOn: '' },
  })
  coolOffData: {
    count: number;
    coolOffEndsOn: string;
    coolOffStartedOn: '';
  };

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: {
      maritalInfo: '',
      spouseName: '',
      motherName: '',
      dependents: 0,
      vehicleInfo: [],
      educationInfo: '',
      residentialInfo: '',
      employmentInfo: '',
      salaryInfo: 0,
    },
  })
  otherInfo: {
    maritalInfo: string;
    spouseName: string;
    motherName: string;
    dependents: number;
    vehicleInfo: string[];
    educationInfo: string;
    residentialInfo: string;
    employmentInfo: string;
    salaryInfo: string;
  };

  @ForeignKey(() => registeredUsers)
  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  userId: string;

  @HasOne(() => registeredUsers, { sourceKey: 'userId', foreignKey: 'id' })
  userData: registeredUsers;

  @ForeignKey(() => loanTransaction)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    unique: true,
  })
  loanId: number;

  @HasOne(() => loanTransaction, { sourceKey: 'loanId', foreignKey: 'id' })
  loanData: loanTransaction;
}
