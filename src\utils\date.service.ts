// Imports
import { Injectable } from '@nestjs/common';
import {
  HTTPError,
  raiseBadRequest,
  raiseParamInvalid,
  raiseParamMissing,
} from 'src/config/error';
import { kGlobalDateTrail } from 'src/constant/global';

type FormatTypes = 'DD-MM-YYYY' | 'MM-DD-YYYY' | 'YYYY-MM-DD' | 'DD/MM/YYYY';
@Injectable()
export class DateService {
  static aadhaarDateStrToDOBDate(dateOfBirth) {
    if (dateOfBirth.includes('/')) {
      const modifiedDOB = dateOfBirth.split('/').reverse().join('-');
      const aadharDOB = modifiedDOB.split('-');

      aadharDOB[1] = this.addLeadingZero(parseInt(aadharDOB[1], 10));
      aadharDOB[2] = this.addLeadingZero(parseInt(aadharDOB[2], 10));

      return this.getGlobalDate(
        new Date(aadharDOB[0] + '-' + aadharDOB[1] + '-' + aadharDOB[2]),
      );
    }
    return this.getGlobalDate(new Date(dateOfBirth));
  }

  private static getGlobalDate(experimentDate: Date) {
    const currentDate = new Date(experimentDate);
    currentDate.setMinutes(currentDate.getMinutes() + 330);
    const currentStatic =
      currentDate.toJSON().substring(0, 10) + kGlobalDateTrail;

    return new Date(currentStatic);
  }

  private static addLeadingZero(number) {
    return number < 10 ? `0${number}` : `${number}`;
  }

  static difference(nextDate: Date, currentdate: Date, type = 'Days') {
    let fromTime;
    let toTime;
    if (type == 'Days' || type === 'Years') {
      fromTime = this.getGlobalDate(currentdate).getTime();
      toTime = this.getGlobalDate(nextDate).getTime();
    } else {
      fromTime = currentdate.getTime();
      toTime = nextDate.getTime();
    }
    let difference = fromTime - toTime;
    difference = Math.abs(difference);
    if (type == 'Seconds')
      difference = Math.floor(Math.ceil(difference / (1000 * 60)) * 60);
    else if (type == 'Minutes')
      difference = Math.ceil(difference / (1000 * 60));
    else if (type == 'Hours')
      difference = Math.ceil(difference / (1000 * 3600));
    else if (type == 'Days')
      difference = Math.ceil(difference / (1000 * 3600 * 24));
    else if (type == 'Month')
      difference = Math.ceil(difference / (1000 * 3600 * 24 * 30));
    else if (type == 'Years')
      difference = Math.floor(Math.ceil(difference / (1000 * 3600 * 24)) / 365);
    return difference;
  }

  getGlobalDate(experimentDate: Date) {
    const currentDate = new Date(experimentDate);
    currentDate.setMinutes(currentDate.getMinutes() + 330);
    const currentStatic =
      currentDate.toJSON().substring(0, 10) + kGlobalDateTrail;

    return new Date(currentStatic);
  }

  getTodayGlobalDate() {
    return this.getGlobalDate(new Date());
  }

  strToDate(dateString: string, dateFormat: 'DDMMYYYY' | 'YYYYMMDD'): Date {
    if (dateFormat == 'DDMMYYYY') {
      const ddStr = dateString.substring(0, 2);
      const mmStr = dateString.substring(2, 4);
      const yyyyStr = dateString.substring(4);

      const target_date_str = `${yyyyStr}-${mmStr}-${ddStr}${kGlobalDateTrail}`;
      return new Date(target_date_str);
    } else if (dateFormat == 'YYYYMMDD') {
      const ddStr = dateString.substring(6);
      const mmStr = dateString.substring(4, 6);
      const yyyyStr = dateString.substring(0, 4);

      const target_date_str = `${yyyyStr}-${mmStr}-${ddStr}${kGlobalDateTrail}`;
      return new Date(target_date_str);
    } else {
      throw HTTPError({});
    }
  }

  difference(nextDate: Date, currentdate: Date, type = 'Days') {
    let fromTime;
    let toTime;
    if (type == 'Days' || type === 'Years') {
      fromTime = this.getGlobalDate(currentdate).getTime();
      toTime = this.getGlobalDate(nextDate).getTime();
    } else {
      fromTime = currentdate.getTime();
      toTime = nextDate.getTime();
    }
    let difference = fromTime - toTime;
    difference = Math.abs(difference);
    if (type == 'Seconds')
      difference = Math.floor(Math.ceil(difference / (1000 * 60)) * 60);
    else if (type == 'Minutes')
      difference = Math.ceil(difference / (1000 * 60));
    else if (type == 'Hours')
      difference = Math.ceil(difference / (1000 * 3600));
    else if (type == 'Days')
      difference = Math.ceil(difference / (1000 * 3600 * 24));
    else if (type == 'Month')
      difference = Math.ceil(difference / (1000 * 3600 * 24 * 30));
    else if (type == 'Years')
      difference = Math.floor(Math.ceil(difference / (1000 * 3600 * 24)) / 365);
    return difference;
  }

  aadhaarDateStrToDOBDate(dateOfBirth) {
    if (dateOfBirth.includes('/')) {
      const modifiedDOB = dateOfBirth.split('/').reverse().join('-');
      const aadharDOB = modifiedDOB.split('-');

      aadharDOB[1] = this.addLeadingZero(parseInt(aadharDOB[1], 10));
      aadharDOB[2] = this.addLeadingZero(parseInt(aadharDOB[2], 10));

      return this.getGlobalDate(
        new Date(aadharDOB[0] + '-' + aadharDOB[1] + '-' + aadharDOB[2]),
      );
    }
    return this.getGlobalDate(new Date(dateOfBirth));
  }

  minutesToFormattedStr(totalMinutes: number) {
    const absTotal = Math.abs(totalMinutes);
    const mins = absTotal % 60;
    const hours = Math.floor(absTotal / 60);
    const days = Math.floor(hours / 24);
    const hourss = hours % 24;
    return days + 'd, ' + hourss + 'h, ' + mins + 'm';
  }

  private addLeadingZero(number) {
    return number < 10 ? `0${number}` : `${number}`;
  }

  private isValidDate(date: any): Boolean {
    return date instanceof Date && !isNaN(date.getTime());
  }

  utcDateRange(minDate: Date | String, maxDate: Date | String) {
    if (!minDate) raiseParamMissing('minDate');
    if (!maxDate) raiseParamMissing('maxDate');
    if ((typeof minDate === 'string') !== (typeof maxDate === 'string'))
      raiseBadRequest('minDate and maxDate must be of the same type');

    const date1: Date =
      typeof minDate === 'string' ? new Date(minDate) : (minDate as Date);
    if (!this.isValidDate(date1)) raiseParamInvalid('minDate');

    const date2: Date =
      typeof maxDate === 'string' ? new Date(maxDate) : (maxDate as Date);
    if (!this.isValidDate(date2)) raiseParamInvalid('maxDate');

    if (date2.getTime() < date1.getTime())
      raiseBadRequest('minDate must be less than or equal to maxDate');

    date1.setUTCDate(date1.getUTCDate() - 1);
    date1.setUTCHours(23);
    date1.setUTCMinutes(60);
    date1.setUTCSeconds(0);

    date2.setUTCHours(23);
    date2.setUTCMinutes(60);
    date2.setUTCSeconds(0);

    return {
      minRange: date1,
      maxRange: date2,
    };
  }

  dateToJsonStr(targetDate: Date, format = 'DD-MM-YYYY', connector = '-') {
    if (!this.isValidDate(targetDate)) raiseParamInvalid('minDate');
    const jsonDate = targetDate.toJSON();
    const date = jsonDate.substring(8, 10);
    const month = jsonDate.substring(5, 7);
    const year = jsonDate.substring(0, 4);
    if (format == 'DD-MM-YYYY')
      return `${date}${connector}${month}${connector}${year}`;
    else if (format == 'YYYY-MM-DD')
      return `${year}${connector}${month}${connector}${date}`;
    return `${date}-${month}-${year}`;
  }

  dateToReadableFormat(targetDate: Date, format: FormatTypes = 'DD-MM-YYYY') {
    if (!this.isValidDate(targetDate)) raiseParamInvalid('minDate');
    const jsonStr = targetDate.toJSON();

    const hours = targetDate.getHours();
    const minutes = targetDate.getMinutes();
    const dd = jsonStr.slice(8,10);
    const mm = jsonStr.slice(5, 7);
    const yyyy = jsonStr.slice(0, 4);
    let readableStr =
      format == 'MM-DD-YYYY'
        ? mm + dd + yyyy
        : format == 'YYYY-MM-DD'
        ? yyyy + mm + dd
        : dd + mm + yyyy;
    if (format == 'DD/MM/YYYY') readableStr = readableStr.replace(/-/g, '/');

    const finalizedData = {
      meridiem: hours >= 12 ? 'PM' : 'AM',
      hours: `${
        hours <= 9
          ? '0' + hours
          : hours > 12
          ? hours - 12 <= 9
            ? '0' + (hours - 12)
            : hours - 12
          : hours
      }`,
      minutes: `${minutes <= 9 ? '0' + minutes : minutes}`,
      readableStr,
    };
    return finalizedData;
  }
}
