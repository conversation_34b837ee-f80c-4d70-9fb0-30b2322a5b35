// Imports
import { Injectable } from '@nestjs/common';
import { ApiService } from 'src/utils/api.service';
import { nMicroAlert } from 'src/constant/networks';

@Injectable()
export class MicroAlertService {
  constructor(private readonly api: ApiService) {}

  async sendWhatsAppMsg(reqData) {
    console.log({ s: nMicroAlert.sendWaMsg });
    const response = await this.api.post(nMicroAlert.sendWaMsg);
    return { reqData, response };
  }
}
