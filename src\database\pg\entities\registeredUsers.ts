// Imports
import {
  Table,
  Column,
  Model,
  DataType,
  AfterFind,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { decryptPhone } from 'src/utils/crypt';
import { KYCEntity } from './kyc.entity';
import { MasterEntity } from './master.entity';

@Table({})
export class registeredUsers extends Model<registeredUsers> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    allowNull: false,
    unique: true,
  })
  uniqueId: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  fullName: string;

  @Column({
    type: DataType.ENUM,
    values: ['Male', 'Female', 'MALE', 'FEMALE'],
    allowNull: true,
  })
  gender: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  email: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    unique: true,
  })
  phone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  state: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'city',
  })
  city: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
  })
  completedLoans: number;

  @Column({ type: DataType.JSONB, allowNull: true })
  lastCrm: any;

  @Column({ type: DataType.STRING, allowNull: true })
  lastOnlineTime: string;

  @ForeignKey(() => KYCEntity)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  kycId: number;

  @BelongsTo(() => KYCEntity)
  kycData: KYCEntity;

  @ForeignKey(() => MasterEntity)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  masterId: number;

  @BelongsTo(() => MasterEntity)
  masterData: MasterEntity;

  // Decrypt the phone numbers
  @AfterFind
  static formatAfterFind(
    instances: registeredUsers[] | registeredUsers,
    options: any,
  ) {
    // If multiple records are found (findAll)
    if (Array.isArray(instances)) {
      if (instances.length > 0) {
        if (instances[0].phone) {
          instances.forEach((instance) => {
            if (instance.phone) {
              instance.phone = decryptPhone(instance.phone);
            }
          });
        }
      }
    } // Single record (findOne)
    else if (instances) {
      if (instances.phone) {
        instances.phone = decryptPhone(instances.phone);
      }

      if (instances.kycData) {
        if (instances.kycData.aadhaarDOB || instances.kycData.aadhaarState) {
          if (options.afterFindOptions) {
            KYCEntity.formatAfterFind(instances.kycData, options);
          }
        }
      }
    }
  }
}
