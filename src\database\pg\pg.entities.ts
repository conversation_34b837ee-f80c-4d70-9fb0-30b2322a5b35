// Imports
import { EmiEntity } from './entities/emi.entity';
import { KYCEntity } from './entities/kyc.entity';
import { BankingEntity } from './entities/banking.entity';
import { loanTransaction } from './entities/loanTransaction';
import { registeredUsers } from './entities/registeredUsers';
import { CibilScoreEntity } from './entities/cibil.score.entity';
import { TransactionEntity } from './entities/transaction.entity';
import { PredictionEntity } from './entities/prediction.entity';
import { ExperianScoreEntity } from './entities/experian.entity';
import { MasterEntity } from './entities/master.entity';
import { admin } from './entities/admin';
import { crmActivity } from './entities/crmActivity';
import { crmDisposition } from './entities/crmDisposition';
import { crmStatus } from './entities/crmStatus';
import { crmTitle } from './entities/crmTitle';
import { CrmReasonEntity } from './entities/crmReason.entity';
import { Department } from './entities/department';

export const PG_CORE_ENTITIES = [
  BankingEntity,
  CibilScoreEntity,
  EmiEntity,
  KYCEntity,
  loanTransaction,
  MasterEntity,
  PredictionEntity,
  registeredUsers,
  TransactionEntity,
  admin,
  crmActivity,
  crmDisposition,
  crmStatus,
  crmTitle,
  CrmReasonEntity,
  Department
];

export const FIN_360_ENTITIES = [ExperianScoreEntity];
