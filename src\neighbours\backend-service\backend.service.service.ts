// Imports
import { Env } from 'src/config/env';
import { Injectable } from '@nestjs/common';
import { ApiService } from 'src/utils/api.service';
import { nBackendService } from 'src/constant/networks';
import { kRouteEnums } from 'src/constant/objects';

@Injectable({})
export class BackendServiceService {
  constructor(private readonly api: ApiService) {}

  async experianSoftHit(memberRefNo: string) {
    let refId = memberRefNo;
    if (!refId.includes(Env.thirdParty.experian['nbfc-prefix'])) {
      refId = Env.thirdParty.experian['nbfc-prefix'] + refId;
    }

    const body = {
      'Member Reference Number': refId,
    };

    return await this.api.post(nBackendService.experianSoftHit, body);
  }

  async recheckFVAutomation() {
    const headers = {
      'qa-test-key': Env.neighbours.backendService.qa_test_key,
    };
    // Gather data -> Final verification list
    const response = await this.api.get(
      nBackendService.finalApprovalList,
      {},
      headers,
    );

    console.log({ response });

    // Iteration -> Final approval automation check
    let auto_passed = 0;
    if (response.response?.data?.rows) {
      for (
        let index = 0;
        index < response.response?.data?.rows.length;
        index++
      ) {
        console.log({ index });
        const data = response.response?.data?.rows[index];
        const body = { userId: data.userId, loanId: data['Loan id'] };

        const automationRes = await this.api.post(
          nBackendService.checkFinalApproval,
          body,
          headers,
        );
        const finalRes = automationRes.response?.data ?? {};

        // Automation success
        if (finalRes.continueRoute == kRouteEnums.SELECT_LOAN_AMOUNT) {
          auto_passed++;
        }
        console.log({ auto_passed });
      }
    }

    return { auto_passed, total: response.response?.data?.rows?.length ?? 0 };
  }
}
