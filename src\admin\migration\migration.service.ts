// Imports
import { Injectable } from '@nestjs/common';
import { MigrationQuery } from './migration.query';
import { UserService } from '../user/user.service';
// import { PgService } from 'src/database/pg/pg.service';
// import { PredictionEntity } from 'src/database/pg/entities/prediction.entity';
import { DataCodesService } from 'src/neighbours/data-codes/data.codes.service';

@Injectable()
export class MigrationService {
  constructor(
    // private readonly pg: PgService,
    private readonly query: MigrationQuery,
    private readonly userService: UserService,
    private readonly dataCodes: DataCodesService,
  ) {}

  async updateMissingInternalScore(reqData) {
    const isReadOnly = reqData.readOnly == 'true' || reqData.readOnly == true;

    const targetData = await this.query.dataForMissingInternalScore();

    if (isReadOnly) {
      return targetData;
    }

    for (let index = 0; index < targetData.rows.length; index++) {
      try {
        console.log({ index, total: targetData.rows.length });
        const response = await this.dataCodes.calculateInternalScore({
          loanId: targetData.rows[index].id,
        });
        if (response.score) {
          // const predictionId = targetData.rows[index]?.predictionData?.id;
          // await this.pg.update(
          //   PredictionEntity,
          //   { CFLScore: response.score },
          //   { limit: 1, silent: true, where: { id: predictionId } },
          // );
        }
      } catch (error) {}
    }

    return {};
  }

  async syncLeadScore(reqData) {
    const user_ids = await this.query.dataForMissingLeadScore(reqData);

    for (let index = 0; index < user_ids.length; index++) {
      try {
        const userId = user_ids[index];
        const response = await this.userService.calculateLeadScore({ userId });
        console.log({ response });
      } catch (error) {}
    }

    return { user_ids };
  }
}
