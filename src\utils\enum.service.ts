// Imports
import { Injectable } from '@nestjs/common';
import { kUserStageEnums } from 'src/constant/strings';

@Injectable()
export class EnumService {
  getUserStage(stage: number) {
    let user_stage = '';
    switch (stage) {
      case 1:
        user_stage = kUserStageEnums.PHONE_VERIFICATION;
        break;

      case 2:
        user_stage = kUserStageEnums.BASIC_DETAILS;
        break;

      case 3:
        user_stage = kUserStageEnums.SELFIE;
        break;

      case 6:
        user_stage = kUserStageEnums.AADHAAR;
        break;

      case 7:
        user_stage = kUserStageEnums.EMPLOYMENT;
        break;

      case 8:
        user_stage = kUserStageEnums.BANKING;
        break;

      case 10:
        user_stage = kUserStageEnums.LOAN_ACCEPT;
        break;

      case 13:
        user_stage = kUserStageEnums.FINAL_VERIFICATION;
        break;

      case 14:
        user_stage = kUserStageEnums.MANDATE;
        break;

      case 15:
        user_stage = kUserStageEnums.ESIGN;
        break;

      case 21:
        user_stage = kUserStageEnums.EXPRESS_REAPPLY;
        break;

      case 20:
        user_stage = kUserStageEnums.NO_ROUTE;
        break;

      case 23:
        user_stage = kUserStageEnums.ON_HOLD;
        break;

      default:
        break;
    }

    if (!user_stage) console.log({ stage, user_stage });

    return user_stage;
  }
}
