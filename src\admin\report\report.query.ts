// Imports
import { Injectable } from '@nestjs/common';
import { raiseParamMissing } from 'src/config/error';
import { PgService } from 'src/database/pg/pg.service';
import { CSE_ROLE_ID, kGlobalDateTrail } from 'src/constant/global';
import { EmiEntity } from 'src/database/pg/entities/emi.entity';
import { TransactionEntity } from 'src/database/pg/entities/transaction.entity';
import { crmActivity } from 'src/database/pg/entities/crmActivity';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import { admin } from 'src/database/pg/entities/admin';

@Injectable()
export class ReportQueryService {
  constructor(private readonly pg: PgService) {}

  async dataForCollectionEfficiency(reqData) {
    let start_date: string = reqData.start_date;
    if (!start_date) raiseParamMissing('start_date');
    let end_date: string = reqData.end_date;
    if (!end_date) raiseParamMissing('end_date');
    start_date = start_date.substring(0, 10) + kGlobalDateTrail;
    end_date = end_date.substring(0, 10) + kGlobalDateTrail;

    const current_month_emi_query = `SELECT "emi_date", "loanId", "principalCovered",
    "id", "interestCalculate", "pay_type", "payment_due_status", "payment_done_date"  
    FROM "EmiEntities"

    WHERE ("emi_date" BETWEEN '${start_date}' AND '${end_date}') AND 
    ("payment_done_date" IS NULL OR "payment_done_date" >= '${start_date}')
    `;

    const currentMonthEmiList: EmiEntity[] = await this.pg.query(
      current_month_emi_query,
    );

    const currentMonthEmiIds = currentMonthEmiList.map((el) => el.id);
    const current_month_payment_query = `SELECT "emiId", "principalAmount", "interestAmount", "maxDPD"
    FROM "TransactionEntities" WHERE ("completionDate" BETWEEN '${start_date}' AND '${end_date}') AND
    "status" = 'COMPLETED' AND "emiId" IN (${currentMonthEmiIds.join(',')})`;

    const currentMonthPayments: TransactionEntity[] = await this.pg.query(
      current_month_payment_query,
    );
    const currentMonthPaymentData = {};
    const current_month_emis = [];
    currentMonthPayments.forEach((el) => {
      if (!currentMonthPaymentData[el['emiId']]) {
        current_month_emis.push(el['emiId']);
        currentMonthPaymentData[el['emiId']] = [];
      }

      currentMonthPaymentData[el['emiId']].push(el);
    });

    let current_month_od_paid_query = `SELECT "principalAmount", "interestAmount" 
    FROM "TransactionEntities"
    WHERE ("completionDate" BETWEEN '${start_date}' AND '${end_date}') AND
    "status" = 'COMPLETED' AND "maxDPD" != 0 AND "emiId" NOT IN (${current_month_emis.join(',')})`;
    const od_paid_transaction_list = await this.pg.query(
      current_month_od_paid_query,
    );

    let carry_forward_od_principal_paid = 0;
    let carry_forward_od_interest_paid = 0;
    od_paid_transaction_list.forEach((el) => {
      carry_forward_od_principal_paid += el.principalAmount;
      carry_forward_od_interest_paid += el.interestAmount;
    });

    return {
      currentMonthEmiList,
      currentMonthPaymentData,
      carry_forward_od_principal_paid,
      carry_forward_od_interest_paid,
    };
  }

  async dataForCsePerformanceReport(reqData) {
    const { minRange, maxRange, needRawData } = reqData;

    let admin_query = `SELECT "id", "fullName", "roleId" FROM "admins"`;
    if (!needRawData) admin_query += `WHERE "roleId" = ${CSE_ROLE_ID};`;

    const adminData: admin[] = await this.pg.query(admin_query);

    let cseAdminIds = [];
    if (!needRawData) cseAdminIds = adminData?.map((ele) => ele.id);
    else
      cseAdminIds = adminData
        .filter((ele) => ele?.roleId == CSE_ROLE_ID)
        ?.map((ele) => ele.id);

    const crm_query = `WITH "crmActivities" AS (
    SELECT  "id", "userId", "createdAt", "loanId", "adminId", DATE("createdAt") AS "createdDate",
    ROW_NUMBER() OVER (PARTITION BY "loanId", DATE("createdAt")) AS "latestActivityRank"
    FROM "crmActivities"
    WHERE
      "createdAt" >= '${minRange.toJSON()}' AND
      "createdAt" <= '${maxRange.toJSON()}' AND
      "loanId" IS NOT NULL AND
      "adminId" IN (${cseAdminIds.map((id) => `'${id}'`).join(',')})
    )
    SELECT * FROM "crmActivities" 
    WHERE "latestActivityRank" = 1;`;

    const crmData: crmActivity[] = cseAdminIds?.length
      ? await this.pg.query(crm_query)
      : [];

    const loanAttributes = [
      'id',
      'loanStatus',
      'netApprovedAmount',
      'loanDisbursementDateTime',
      'loanRejectDateTime',
    ];
    if (needRawData)
      loanAttributes.push(
        ...['fullName', 'phone', 'completedLoan', 'manualVerificationAcceptId'],
      );

    let loan_query = `SELECT ${loanAttributes.map((ele) => `"${ele}"`).join(',')}
    FROM "loanTransactions"
	  WHERE
    ("loanDisbursementDateTime" >= '${minRange.toJSON()}' AND "loanDisbursementDateTime" <= '${maxRange.toJSON()}') 
    OR 
	  ("loanRejectDateTime" >= '${minRange.toJSON()}' AND "loanRejectDateTime" <= '${maxRange.toJSON()}')`;

    if (crmData.length) {
      const crmLoanIds = [...new Set(crmData.map((crm) => `'${crm.loanId}'`))];
      loan_query += `OR "id" IN (${crmLoanIds.join(',')})`;
    }

    const loanData: loanTransaction[] = await this.pg.query(loan_query);

    return { adminData, crmData, loanData };
  }

  async dataForOverallAadhaarStateCollection(reqData) {
    let start_date = reqData.startDate;
    if (!start_date) {
      raiseParamMissing('startDate');
    }
    start_date = start_date + kGlobalDateTrail;
    let end_date = reqData.endDate;
    if (!end_date) {
      raiseParamMissing('endDate');
    }
    end_date = end_date + kGlobalDateTrail;

    const raw_query = `SELECT *
    FROM (SELECT "aadhaarState" AS "Aadhaar State",
          COUNT("LoanDetails"."loanId") AS "Total Due Loans",
            
        (SUM("expected_principal") + SUM("expected_interest")) AS "Expected EMI Amount",
          (SUM("LoanDetails"."paid_principal") + SUM("LoanDetails"."paid_interest")) AS "PAID EMI Amount",
        
        ROUND(CAST(CASE WHEN SUM("expected_principal") > 0
                    THEN ((SUM("LoanDetails"."paid_principal") +  SUM("LoanDetails"."paid_interest")) 
                / (SUM("expected_principal") + SUM("expected_interest")) ) * 100 ELSE 0
          END AS numeric), 2) AS "Overall Collection Efficiency (%)",

          ROUND(COALESCE((SUM(CASE WHEN "max_dpd" = 0 AND "LoanDetails"."paid_principal" > 0
          THEN "LoanDetails"."paid_principal"::numeric END) / SUM("expected_principal")::numeric) * 100, 0), 2) AS "Collection Efficiency (0 DPD)",
          ROUND(COALESCE((SUM(CASE WHEN "max_dpd" BETWEEN 1 AND 30 AND "LoanDetails"."paid_principal" > 0
            THEN "LoanDetails"."paid_principal"::numeric END) / SUM("expected_principal")::numeric) * 100, 0), 2) AS "Collection Efficiency (1–30 DPD)",
          ROUND(COALESCE((SUM(CASE WHEN "max_dpd" BETWEEN 31 AND 90 AND "LoanDetails"."paid_principal" > 0
          THEN "LoanDetails"."paid_principal"::numeric END) / SUM("expected_principal")::numeric) * 100, 0), 2) AS "Collection Efficiency (31–90 DPD)",
        ROUND(COALESCE((SUM(CASE WHEN "max_dpd" > 90 AND "LoanDetails"."paid_principal" > 0
          THEN "LoanDetails"."paid_principal"::numeric END) / SUM("expected_principal")::numeric) * 100, 0), 2) AS "Collection Efficiency (90+ DPD)"
      
        FROM "LoanDetails"
      
      WHERE "expected_principal" > 0 AND "disbursed_date" >= '${start_date}' AND "disbursed_date" <= '${end_date}'
      
        GROUP BY "aadhaarState"
    ) AS subquery

    ORDER BY "Aadhaar State" ASC`;

    return await this.pg.query(raw_query);
  }

  async dataForTotalLoansForAadhaarStateCollection(reqData) {
    let start_date = reqData.startDate;
    if (!start_date) {
      raiseParamMissing('startDate');
    }
    start_date = start_date + kGlobalDateTrail;
    let end_date = reqData.endDate;
    if (!end_date) {
      raiseParamMissing('endDate');
    }
    end_date = end_date + kGlobalDateTrail;

    const raw_query = `SELECT "aadhaarState", CAST(COUNT("loanId") AS NUMERIC) AS "Total Loans",
    CAST(SUM(CASE WHEN "loanStatus" = '1' THEN 1 ELSE 0 END) AS NUMERIC) AS "Paid Loans" 
    FROM "LoanDetails"

    WHERE "disbursed_date" >= '${start_date}' AND "disbursed_date" <= '${end_date}'

    GROUP BY "aadhaarState" ORDER BY "aadhaarState" ASC`;

    return await this.pg.query(raw_query);
  }
}
